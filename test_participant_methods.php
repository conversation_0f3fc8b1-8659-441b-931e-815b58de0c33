<?php
/**
 * Test script to check if the new participant methods work
 */

require_once 'Framework/init.php';

use Systemsight\Framework\Registry;
use Systemsight\Framework\Libraries\DoctrineOrm;

// Initialize Doctrine
DoctrineOrm::init();

// Get a participant to test
$participantRepo = Registry::loadRepository('Services.Participant');
$participant = $participantRepo->findOneBy([]);

if ($participant) {
    echo "Testing participant: " . $participant->getFullName() . "\n";
    echo "STEAM Center: " . $participant->getSteamCenterTitle() . "\n";
    echo "Laboratory: " . $participant->getLaboratoryTitle() . "\n";
    
    $reservation = $participant->getReservation();
    if ($reservation) {
        echo "Reservation ID: " . $reservation->getId() . "\n";
        
        $event = $reservation->getEvent();
        if ($event) {
            echo "Event ID: " . $event->getId() . "\n";
            $city = $event->getCity();
            if ($city) {
                echo "Event City: " . $city->get('title') . "\n";
            }
        }
        
        $service = $reservation->getService();
        if ($service) {
            echo "Service ID: " . $service->getId() . "\n";
            echo "Service Title: " . $service->get('title') . "\n";
            $city = $service->getCity();
            if ($city) {
                echo "Service City: " . $city->get('title') . "\n";
            }
        }
    }
} else {
    echo "No participants found in database\n";
}
