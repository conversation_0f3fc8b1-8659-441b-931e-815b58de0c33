<?php

namespace Project\Modules\Services\Models;

use Doctrine\Common\Collections\ArrayCollection;
use Doctrine\Common\Collections\Collection;
use Doctrine\ORM\PersistentCollection;
use Framework\Libraries\Behaviors\Timestampable\TimestampableTrait;
use Systemsight\Framework\Libraries\Doctrine\SystemsightMetaBuilder;
use Systemsight\Framework\Libraries\DoctrineUtils;
use Systemsight\Framework\Libraries\MoneyUtilities;
use Systemsight\Framework\Libraries\Registry;
use Systemsight\Framework\Libraries\SM\StateMachine;
use Systemsight\Framework\Libraries\Utilities;
use Systemsight\Modules\Datasets\Models\DatasetsClass;
use Systemsight\Modules\Datasets\Models\DatasetsMunicipality;
use Systemsight\Modules\Forms\Models\FormsItem;
use Systemsight\Modules\Invoices\Models\InvoicesItem;
use Systemsight\Modules\Services\Core\SM\SMFactory;
use Systemsight\Modules\Services\Core\ReservationStates;
use Systemsight\Modules\Services\Models\ServicesEvent;
use Systemsight\Modules\Services\Models\ServicesItem;
use Systemsight\Modules\Services\Models\ServicesParticipantsDocument;
use Systemsight\Modules\Services\Models\ServicesParticipant;
use Systemsight\Modules\Services\Models\ServicesState;
use Systemsight\Modules\Services\Tools\ServicesReservationsUtilities;
use Systemsight\Modules\Simplemodule\Models\SimplemoduleEntity;
use Systemsight\Modules\Users\Models\UsersItem;

/**
 * Rezervacijos
 */
class ServicesReservation extends SimplemoduleEntity
{
    //region Properties

    use TimestampableTrait;

    /** @var null|UsersItem */
    public $user;

    /** @var null|ServicesEvent */
    public $event;

    /** @var null|ServicesItem */
    public $service;

    /** @var null|DatasetsMunicipality */
    public $municipality;

    /** @var null|DatasetsClass */
    public $class;

    /** @var ServicesParticipant[]|Collection */
    protected $participants;

    /** @var ServicesState[]|Collection - busenu keitimo istorija */
    public $states;

    public ?InvoicesItem $invoice = null;

    /** @var null|string Būsena */
    public $status = ReservationStates::STATE_DRAFT;

    /** @var string */
    public $hash;
    /** @var string */
    public $public_hash;

    /** @var null|\DateTime */
    public $visit_time;

    /** @var string */
    public $user_hash;

    public $first_name;
    public $last_name;
    public $phone;
    public $email;

    public $institution_title;
    public $taught_subject;

    public $comment = '';

    public $service_price;
    public $invoice_required;
    public $buyer_title;
    public $buyer_code;
    public $buyer_vat_code;
    public $buyer_address;

    public $deleted_at = null;

    /** @var PersistentCollection|ServicesParticipantsDocument[]|null */
    public $documents;

    protected $_sm = null;

    /** @var null|ServicesState */
    protected $_currentStateEntity = false;

    //endregion

    public function __construct()
    {
        $this->hash = Registry::loadRepository('Services.Reservation')->generateHash();
        $this->public_hash = Registry::loadRepository('Services.Reservation')->generateHash('public');
        $this->participants = new ArrayCollection();
        $this->states = new ArrayCollection();
    }

    public static function setFields(SystemsightMetaBuilder $builder)
    {
        $builder->addId();
        $builder->setRepository();
        $builder->setTable('services_reservations');

        $builder->addManyToOne('user', 'Users.Item', null, SystemsightMetaBuilder::ON_DELETE_CASCADE);
        /** @see ServicesEvent::$service_reservation */
        $builder->addManyToOne('event', 'Services.Event', 'service_reservation', SystemsightMetaBuilder::ON_DELETE_CASCADE);
        /** @see ServicesItem::$serices_reservations */
        $builder->addManyToOne('service', 'Services.Item', 'serices_reservations', SystemsightMetaBuilder::ON_DELETE_CASCADE);//Veikla
        $builder->addManyToOne('municipality', 'Datasets.Municipality', null, SystemsightMetaBuilder::ON_DELETE_SET_NULL);
        $builder->addManyToOne('class', 'Datasets.Class', null, SystemsightMetaBuilder::ON_DELETE_SET_NULL);
        /** @see ServicesParticipant::$reservation */
        $builder->addInverseOneToMany('participants', 'Services.Participant', 'reservation',
            ['first_name' => 'ASC', 'last_name' => 'ASC'], SystemsightMetaBuilder::CASCADE_ALL
        );
        /** @see ServicesState::$reservation */
        $builder->addInverseOneToMany('states', 'Services.State', 'reservation', ['id' => 'DESC'], SystemsightMetaBuilder::CASCADE_ALL);
        /** @see InvoicesItem::$reservation */
        $builder->addInverseOneToOne('invoice', 'Invoices.Item', 'reservation');

        $builder->addInverseOneToMany('documents', 'Services.ParticipantsDocument', 'item', ['position' => 'ASC']);

        $builder->addField('hash', 'string');
        $builder->addField('public_hash', 'string', ['length' => 50]);
        $builder->addField('status', 'string');
        $builder->addField('visit_time', 'datetime', ['nullable' => true]);
        $builder->addField('user_hash', 'string', ['nullable' => true]);
        $builder->addField('first_name', 'string', ['nullable' => true]);
        $builder->addField('last_name', 'string', ['nullable' => true]);
        $builder->addField('phone', 'string', ['nullable' => true]);//Kontaktinis telefonas
        $builder->addField('email', 'string', ['nullable' => true]);//Kontaktinis el. paštas
        $builder->addField('institution_title', 'string', ['nullable' => true]);
        $builder->addField('taught_subject', 'string', ['nullable' => true]);
        $builder->addField('comment', 'text', ['nullable' => true]);

        $builder->addMoneyField('service_price', ['nullable' => true]);
        $builder->addField('invoice_required', 'boolean', ['nullable' => true]);
        $builder->addField('buyer_title', 'string', ['nullable' => true]);
        $builder->addField('buyer_code', 'string', ['nullable' => true]);
        $builder->addField('buyer_vat_code', 'string', ['nullable' => true]);
        $builder->addField('buyer_address', 'string', ['nullable' => true]);

        $builder->addTimestampableFields();
        $builder->addBackupField();
    }

    //region Getters

    /**
     * @return null|UsersItem
     */
    public function getUser()
    {
        return DoctrineUtils::getSafeMappedEntity($this->user);
    }

    public function getEvent(): ?ServicesEvent
    {
        return DoctrineUtils::getSafeMappedEntity($this->event);
    }

    public function getMunicipality(): ?DatasetsMunicipality
    {
        return DoctrineUtils::getSafeMappedEntity($this->municipality);
    }

    public function getClass(): ?DatasetsClass
    {
        return DoctrineUtils::getSafeMappedEntity($this->class);
    }

    public function getService(): ?ServicesItem
    {
        return DoctrineUtils::getSafeMappedEntity($this->service);
    }

    public function getStatusTitle(): string
    {
        return ServicesReservationsUtilities::formatStateName($this->status);
    }

    public function getFullName(): string
    {
        return trim($this->first_name . ' ' . $this->last_name);
    }

    public function getPrice()
    {
        return $this->service_price / 100;
    }

    public function getServicePriceFormatted($includeCurrency = true): string
    {
        return MoneyUtilities::formatMoney($this->getPrice(), $includeCurrency);
    }

    public function getTotalPrice()
    {
        return ($this->service_price * $this->getArrivedParticipantsCount()) / 100;
    }

    public function getTotalPriceFormatted($includeCurrency = true): string
    {
        return MoneyUtilities::formatMoney($this->getTotalPrice(), $includeCurrency);
    }

    /**
     * Get the STEAM center (city) title for this reservation
     * @return string
     */
    public function getSteamCenterTitle(): string
    {
        // Try to get city from event first
        if ($event = $this->getEvent()) {
            if ($cityTitle = $event->getCityTitle()) {
                return $cityTitle;
            }
        }

        // Fallback to service city
        if ($service = $this->getService()) {
            if ($cityTitle = $service->getCityTitle()) {
                return $cityTitle;
            }
        }

        return '-';
    }

    /**
     * Get the laboratory (service) title for this reservation
     * @return string
     */
    public function getLaboratoryTitle(): string
    {
        if ($service = $this->getService()) {
            return $service->get('title');
        }

        return '-';
    }

    //endregion
    //region Helpers

    public function getExpireDatetime(): ?\DateTime
    {
        if(!$minutes = Utilities::getConfig('services_draft_lifetime')) {
            $minutes = 15;
        }
        $dateTimeEnd = clone $this->created_at;
        $dateTimeEnd->add(new \DateInterval("PT{$minutes}M"));

        return $dateTimeEnd;
    }

    /**
     * Grazina laika minutemis, kiek liko iki rezervacijos pabaigos (iki bus patvirtinta/uzbaigta registracija)
     * @return float|int
     */
    public function getRemainingTime()
    {
        $dateTimeEnd = $this->getExpireDatetime();

        $dateTimeNow = new \DateTime();

        if ($dateTimeNow->format('Y-m-d H:i') == $dateTimeEnd->format('Y-m-d H:i')) {
            return 0;
        }

        $interval = $dateTimeNow->diff($dateTimeEnd);

        return $this->_countMinutes($interval);
    }

    /**
     * Pagal pateikta intervala suskaiciuoja minutes
     *
     * @param \DateInterval $interval
     * @return float|int
     */
    protected function _countMinutes(\DateInterval $interval)
    {
        $minutes = 0;

        if (!empty($interval->days)) {
            $minutes += $interval->days * 24 * 60;
        }
        if (!empty($interval->h)) {
            $minutes += $interval->h * 60;
        }

        $minutes += $interval->i;

        if(!empty($interval->invert)) {
            $minutes *= -1;
        }

        return $minutes;
    }

    public function isAccessible(): bool
    {
        return in_array($this->status, [
            ReservationStates::STATE_SUBMITTED,
            ReservationStates::STATE_RETURNED,
            ReservationStates::STATE_CONFIRMED,
            ReservationStates::STATE_COMPLETED,
        ]);
    }

    public function isExpired(): bool
    {
        $dateTimeEnd = $this->getExpireDatetime();
        $dateTimeNow = new \DateTime();

        return ($dateTimeNow > $dateTimeEnd);
    }

    public function isEditable(): bool
    {
        $dateNow = new \DateTime();

        return (
            $event = $this->getEvent()
            and ($event->register_to->format('Y-m-d') >= $dateNow->format('Y-m-d'))
            and $this->isReturned()
        );
    }

    public function isDraft(): bool
    {
        return $this->status === ReservationStates::STATE_DRAFT;
    }

    public function isSubmitted(): bool
    {
        return $this->status === ReservationStates::STATE_SUBMITTED;
    }

    public function isReturned(): bool
    {
        return $this->status === ReservationStates::STATE_RETURNED;
    }

    public function isConfirmed(): bool
    {
        return $this->status === ReservationStates::STATE_CONFIRMED;
    }

    public function isCompleted(): bool
    {
        return $this->status === ReservationStates::STATE_COMPLETED;
    }

    public function isConfirmedOrCompleted(): bool
    {
        return in_array($this->status, [
            ReservationStates::STATE_CONFIRMED, ReservationStates::STATE_COMPLETED
        ]);
    }

    public function isUnfulfilled(): bool
    {
        return $this->status === ReservationStates::STATE_UNFULFILLED;
    }

    public function isRejected(): bool
    {
        return in_array($this->status, [
            ReservationStates::STATE_REJECTED, ReservationStates::STATE_USER_REJECTED
        ]);
    }

    public function isSubmitTransition(string $transition)
    {
        return $transition === ReservationStates::TRANSITION_SUBMIT;
    }

    public function isRejectTransition(string $transition)
    {
        return in_array($transition, [
            ReservationStates::TRANSITION_REJECT, ReservationStates::TRANSITION_USER_REJECT
        ]);
    }

    public function isCompleteTransition(string $transition)
    {
        return $transition === ReservationStates::TRANSITION_COMPLETE;
    }

    public function canReject(): bool
    {
        $dateNow = new \DateTime();

        return (
            $event = $this->getEvent()
            and ($event->register_to->format('Y-m-d') >= $dateNow->format('Y-m-d'))
            and !$this->isRejected()
        );
    }

    public function canComplete(): bool
    {
        return (
            $this->isConfirmed()
            and $this->getArrivedParticipantsCount() > 0
        );
    }

    /**
     * @return FormsItem|null
     */
    public function getTeachersSurveyForm(): ?FormsItem
    {
        if($form_id = Utilities::getConfig('services_teachers_survey_form')
            and $form = Registry::loadRepository('Forms.Item')->find($form_id)
        ) {
            return $form;
        }

        return null;
    }

    /**
     * @return FormsItem|null
     */
    public function getStudentsSurveyForm(): ?FormsItem
    {
        if($form_id = Utilities::getConfig('services_students_survey_form')
            and $form = Registry::loadRepository('Forms.Item')->find($form_id)
        ) {
            return $form;
        }

        return null;
    }

    //endregion
    //region Participants

    /**
     * @return ArrayCollection|Collection|ServicesParticipant[]
     */
    public function getParticipants()
    {
        return $this->participants;
    }

    public function addParticipant(ServicesParticipant $participant)
    {
        if(!$this->participants->contains($participant)) {
            $this->participants->add($participant);
        }
    }

    public function getParticipantsCount(): int
    {
        return Registry::loadRepository('Services.Participant')->count([
            'reservation' => $this,
        ]);
    }

    public function getArrivedParticipantsCount(): int
    {
        return Registry::loadRepository('Services.Participant')->count([
            'reservation' => $this,
            'has_arrived' => 1,
        ]);
    }

    public function getParticipantsListToString($glue = '; ')
    {
        $list = [];
        foreach ($this->participants as $participant) {
            $list[] = $participant->getFullName();
        }

        return implode($glue, $list);
    }

    //endregion
    //region State machine

    public function getSM(): StateMachine
    {
        if($this->_sm === null){
            $this->_sm = SMFactory::instance()->get($this, 'status');
        }
        return $this->_sm;
    }

    public function getAvailableTransitions(): array
    {
        $possible = $this->getSM()->getPossibleTransitions();

        return array_diff($possible, [
            ReservationStates::TRANSITION_COMPLETE,
            ReservationStates::TRANSITION_UNFULFILL,
        ]);
    }

    public function getStateObject(): ?ServicesState
    {
        if ($this->_currentStateEntity === false) {
            $this->_currentStateEntity = $this->states->first();
            if ($this->_currentStateEntity === false) {
                $this->_currentStateEntity = null;
            }
        }

        return $this->_currentStateEntity;
    }

    public function addState(ServicesState $state)
    {
        if($this->states->contains($state)) {
            return;
        }

        $this->state = $state->state;
        $this->states->add($state);
        $this->_currentStateEntity = $state;
        $state->setReservation($this);
    }

    public function removeState(ServicesState $state)
    {
        $this->states->removeElement($state);
        $this->_currentStateEntity = false;
        $newState = $this->getStateObject();
        if ($newState) {
            $this->state = $newState->state;
        } else {
            $this->state = null;
        }
    }

    public function formatTransitionName(string $transition)
    {
        return ServicesReservationsUtilities::formatTransitionName($transition);
    }

    //endregion
}
