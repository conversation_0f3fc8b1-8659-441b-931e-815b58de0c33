<?php
/**
 * @copyright JSC Dizaino kryptis 2020
 *
 * This Software is the property of Dizaino Kryptis
 * and is protected by copyright law – it is NOT Freeware.
 *
 * Any unauthorized use of this software without a valid license key
 * is a violation of the license agreement and will be prosecuted by
 * civil and criminal law.
 *
 * Contact JSC Dizaino kryptis:
 * E-mail: <EMAIL>
 * https://www.kryptis.lt
 *
 * <AUTHOR>
 */

namespace Project\Modules\Services\Models;

use Framework\Libraries\Behaviors\Adminstampable\AdminstampableTrait;
use Framework\Libraries\Behaviors\Timestampable\TimestampableTrait;
use Systemsight\Framework\Libraries\Doctrine\SystemsightMetaBuilder;
use Systemsight\Framework\Libraries\DoctrineUtils;
use Systemsight\Modules\Administrators\Models\AdministratorsUser;
use Systemsight\Modules\Simplemodule\Models\SimplemoduleEntity;
use Systemsight\Modules\Services\Models\ServicesReservation;

abstract class ServicesParticipant extends SimplemoduleEntity
{
    use AdminstampableTrait;
    use TimestampableTrait;

    /** @var ServicesReservation */
    public $reservation;

    public $first_name;
    public $last_name;

    /** @var null|boolean */
    public $has_arrived = false;

    public static function setFields(SystemsightMetaBuilder $builder)
    {
        $builder->setRepository();

        $builder->setTable("services_participants");
        $builder->addId();
        $builder->addManyToOne('reservation', 'Services.Reservation', 'participants', SystemsightMetaBuilder::ON_DELETE_CASCADE);

        $builder->addField('first_name', 'string');
        $builder->addField('last_name', 'string');
        $builder->addField('has_arrived', 'boolean', ['nullable' => true]);

        $builder->addAdminstampableFields();
        $builder->addTimestampableFields();
    }

    /**
     * @return null|ServicesReservation
     */
    public function getReservation(): ?ServicesReservation
    {
        return $this->reservation;
    }

    public function getAdmin(): ?AdministratorsUser
    {
        return DoctrineUtils::getSafeMappedEntity($this->admin);
    }

    public function getFullName(): string
    {
        return trim($this->first_name . ' ' . $this->last_name);
    }

    /**
     * Get the STEAM center (city) title for this participant
     * @return string
     */
    public function getSteamCenterTitle(): string
    {
        $reservation = $this->getReservation();
        if (!$reservation) {
            return '-';
        }

        // Try to get city from event first
        if ($event = $reservation->getEvent()) {
            if ($city = $event->getCity()) {
                return $city->get('title');
            }
        }

        // Fallback to service city
        if ($service = $reservation->getService()) {
            if ($city = $service->getCity()) {
                return $city->get('title');
            }
        }

        return '-';
    }

    /**
     * Get the laboratory (service) title for this participant
     * @return string
     */
    public function getLaboratoryTitle(): string
    {
        $reservation = $this->getReservation();
        if (!$reservation) {
            return '-';
        }

        if ($service = $reservation->getService()) {
            return $service->get('title');
        }

        return '-';
    }

    public function offsetExists($offset): bool
    {
        if ($offset == 'title') {
            return true;
        }
        return parent::offsetExists($offset);
    }

    public function offsetGet($offset)
    {
        if ($offset == 'title') {
            return  $this->getFullName();
        }
        return parent::offsetGet($offset);
    }
}
