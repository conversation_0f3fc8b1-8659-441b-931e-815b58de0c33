{$csrfToken = "{csrf_token}"}
{capture assign='baseCertificateLink'}{strip}
    {$seo->getBaseModuleAlias('services/certificate')}/hash.{$reservation->hash|escape:'url'}
{/strip}{/capture}
{capture assign='baseEditLink'}{strip}
    {$seo->getBaseModuleAlias('services/edit_participant')}/hash.{$reservation->hash|escape:'url'}
{/strip}{/capture}
{capture assign='baseRemoveLink'}{strip}
    {$seo->getBaseModuleAlias('services/delete_participant')}/hash.{$reservation->hash|escape:'url'}
{/strip}{/capture}
{$confirmDelete = $l10n->get('confirm delete')|escape:'html'}
{$isCompleted = $reservation->isCompleted()}

<div class="w-100 form-actions d-flex justify-content-end mb-3">
    <a class="btn btn-outline-secondary" data-name="add" href="{$seo->getBaseModuleAlias('services/edit_participant')}/hash.{$reservation->hash|escape:'url'}?_display=iframe">
        <span>{t add}</span>
        <em class="fas fa-plus"></em>
    </a>
</div>

<div class="table-responsive w-100">
    <table class="table table-hover table-striped table-sm">
        <thead>
        <tr>
            <th>{t services: student name surname}</th>
            <th>STEAM centras</th>
            <th>Laboratorija</th>
            <th class="text-center">{t services: generate certificate}</th>
            <th class="text-center">{t certificate}</th>
            <th class="text-end">{t actions title}</th>
        </tr>
        </thead>
        <tbody>
        {foreach $reservation->getParticipants() as $participant}
            <tr>
                <td>{$participant->getFullName()}</td>
                <td>{$participant->getSteamCenterTitle()}</td>
                <td>{$participant->getLaboratoryTitle()}</td>
                <td class="text-center">{if !empty($participant->has_arrived)}{t yes}{else}{t no}{/if}</td>
                <td class="text-center">
                    {if $isCompleted && !empty($participant->has_arrived)}
                        <a href="{$baseCertificateLink}?id={$participant->getId()}&_token={$csrfToken}"
                           class="fs-20 style-secondary">
                            <em class="far fa-file-pdf"></em>
                        </a>
                    {else}
                        <a class="fs-20 style-secondary disabled"><em class="far fa-file-pdf"></em></a>
                    {/if}
                </td>
                <td class="text-end">
                    <a href="{$baseEditLink}?id={$participant->getId()}&_token={$csrfToken}"
                       class="fs-20 style-secondary text-decoration-none mx-1"
                    >
                        <em class="icon-edit"></em>
                    </a>
                    <a href="{$baseRemoveLink}?id={$participant->getId()}&_token={$csrfToken}"
                       class="fs-20 style-danger text-decoration-none mx-1"
                       data-confirm="{$confirmDelete}"
                    >
                        <em class="icon-remove-circle"></em>
                    </a>
                </td>
            </tr>
        {/foreach}
        </tbody>
    </table>
</div>
