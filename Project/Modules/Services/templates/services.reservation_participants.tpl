{extends "services.base_reservations_layout.tpl"}

{block name="block_reservation_content"}
    <h3 class="mb-4">{t services: participants and certificates}</h3>

    {$participants = $reservation->getParticipants()}
    {if !empty($participants) && $participants->count()}
        {$csrfToken = "{csrf_token}"}
        {capture assign='baseCertificateLink'}{strip}
            {$seo->getBaseModuleStructureAlias('services/certificate')}/hash.{$reservation->hash|escape:'url'}
        {/strip}{/capture}
        {$editable = $reservation->isEditable()}
        {$isCompleted = $reservation->isCompleted()}
        {if !empty($editable)}
            {capture assign='baseRemoveLink'}{strip}
                {$seo->getBaseModuleStructureAlias('services/delete_participant')}/hash.{$reservation->hash|escape:'url'}
            {/strip}{/capture}
            {$confirmDelete = $l10n->get('confirm delete')|escape:'html'}
        {/if}
        <table class="table table-participants">
            <thead>
                <tr>
                    <th class="">{t services: student name surname}</th>
                    <th class="">STEAM centras</th>
                    <th class="">Laboratorija</th>
                    <th class="">{t status}</th>
                    <th class="d-flex justify-content-md-between flex-column flex-md-row">
                        <span>{t certificate}</span>
                        {if $reservation->isCompleted()}<a href="{$baseCertificateLink}?_token={$csrfToken}" class="text-secondary">{$l10n->get('services: all certificates')}&nbsp;<em class="far fa-file-archive"></em></a>{/if}
                    </th>
                    {if !empty($editable)}
                        <th class="text-end">{t actions title}</th>
                    {/if}
                </tr>
            </thead>
            <tbody>
                {foreach $participants as $participant}
                    <tr>
                        <td>{$participant->getFullName()}</td>
                        <td>{$participant->getSteamCenterTitle()}</td>
                        <td>{$participant->getLaboratoryTitle()}</td>
                        <td>
                            {if !empty($participant->has_arrived)}
                                <span class="badge badge-arrived">{t arrived}</span>
                            {else}
                                <span class="badge badge-not-arrived">{t did not arrive}</span>
                            {/if}
                        </td>
                        <td>
                            {if $isCompleted && !empty($participant->has_arrived)}
                                <a href="{$baseCertificateLink}?id={$participant->getId()}&_token={$csrfToken}"
                                   class="fs-20 style-secondary">
                                    <em class="far fa-file-pdf"></em>
                                </a>
                            {else}
                                <a class="fs-20 style-secondary disabled"><em class="far fa-file-pdf"></em></a>
                            {/if}
                        </td>
                        {if !empty($editable)}
                            <td class="text-end">
                                <a href="{$baseRemoveLink}?id={$participant->getId()}&_token={$csrfToken}"
                                   class="fs-20 style-secondary"
                                   data-confirm="{$confirmDelete}"
                                >
                                    <em class="icon-remove-circle"></em>
                                </a>
                            </td>
                        {/if}
                    </tr>
                {/foreach}
            </tbody>
        </table>
    {else}
        <p class="alert alert-info">{t services: empty participants list}</p>
    {/if}

{/block}
