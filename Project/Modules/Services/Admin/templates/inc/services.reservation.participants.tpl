{foreach $item->getParticipants() as $key => $participant}
    <div class="text-nowrap">
        <span>{$participant->getFullName()}</span>
        <br><small class="text-muted">STEAM centras: {$participant->getSteamCenterTitle()}</small>
        <br><small class="text-muted">Laboratorija: {$participant->getLaboratoryTitle()}</small>
        {if $item->isCompleted()}
            {if $participant->has_arrived}
                {**}
            {else}
                <button class="btn btn-sm btn-success services-change-participant-has_arrived" data-id="{$participant->getId()}" type="button">
                    <i class="fa fa-check" aria-hidden="true"></i>{$l10n->get('services: generate certificate')}
                </button>
            {/if}
            {if $participant->has_arrived}
                {include file="`$smarty.const.FW_PROJECT`/Modules/Services/Admin/templates/inc/certificate_action.tpl" item=$participant}
            {/if}
        {/if}
    </div>
{/foreach}
