<?php
/**
 * @copyright JSC Dizaino kryptis 2021
 *
 * This Software is the property of Dizaino Kryptis
 * and is protected by copyright law – it is NOT Freeware.
 *
 * Any unauthorized use of this software without a valid license key
 * is a violation of the license agreement and will be prosecuted by
 * civil and criminal law.
 *
 * Contact JSC Dizaino kryptis:
 * E-mail: <EMAIL>
 * https://www.kryptis.lt
 *
 * <AUTHOR>
 */

namespace Project\Modules\Services\Forms;

use Symfony\Component\Validator\Constraints\Range;
use Systemsight\Framework\Libraries\Registry;
use Systemsight\Framework\Libraries\Validators\Phone;
use Systemsight\Modules\Services\Models\ServicesEvent;
use Systemsight\Modules\Services\Models\ServicesReservation;
use Systemsight\Modules\Services\Validators\ServicesReservationParticipants;
use Systemsight\Modules\Services\Validators\ServicesReservationVisitTime;
use Systemsight\Modules\Simplemodule\Tools\ManageBuilder;
use Systemsight\Modules\Users\Models\UsersItem;

/**
 * Rezervacijos redagavimo ManageBuilder'is, kai red<PERSON><PERSON> STEAM centro darbuotojas
 */
class ReservationManageForm extends ManageBuilder
{
    public function __construct($entity = null)
    {
        /** @var ServicesReservation $entity */
        parent::__construct($entity);

        if($user = $entity->getUser()) {
            $this->addField('user', [
                'type' => 'string',
                'html' => '<div class="dummy_field">' . $user->getTitle() . '</div>',
            ]);
        }

        $service = $entity->getService();

        $invoiceConditionals = [
            [
                'show' => true,
                'when' => 'invoice_required',
                'eq' => true
            ]
        ];

        $this
            // ->addField('event', [
            //     'type' => ManageBuilder::TYPE_SELECT2,
            //     'label' => 'services: activity schedule',
            //     'entity' => 'Services.Event',
            //     'values' => Registry::loadRepository('Services.Event')->getSelectList([
            //         'service' => $service,
            //     ]),
            //     'validation' => ['not_blank'],
            //     'id' => 'service_event',
            //     'col' => 'col-sm-6',
            // ])
            ->addField('visit_time', [
                'type' => ManageBuilder::TYPE_SELECT2,
                'label' => 'visit datetime',
                'values' => [],
                'value' => !empty($entity->visit_time) ? $entity->visit_time->format('Y-m-d H:i:s') : null,
                'input_options' => [
                    'data_url' => Registry::seo()->getBaseModuleAlias('services/ajax_get_visit_times'),
                    'depends_on' => [
                        'service_event'
                    ],
                ],
                'selectFirst' => true,
                'validation' => [
                    'not_blank',
                    new ServicesReservationVisitTime([
                        'skip_range' => true,
                        'reservation' => $entity,
                        'service' => $service
                    ]),
                ],
                'col' => 'col-sm-6',
            ])

            ->addField('first_name', [
                'type' => ManageBuilder::TYPE_TEXT,
                'label' => 'teacher first name',
                'value' => $entity->first_name ?? $user->first_name ?? null,
                'col' => 'col-sm-6'
            ])
            ->addField('last_name', [
                'type' => ManageBuilder::TYPE_TEXT,
                'label' => 'teacher last name',
                'value' => $entity->last_name ?? $user->last_name ?? null,
                'col' => 'col-sm-6'
            ])
            ->addField('municipality', [
                'type' => ManageBuilder::TYPE_SELECT2,
                'label' => 'services: municipality',
                'entity' => 'Datasets.Municipality',
                'loadValues' => ['repository' => 'Datasets.Municipality', 'method' => 'getList'],
                'validation' => 'not_blank',
                //'col' => 'col-sm-6',
            ])
            ->addField('institution_title', [
                'type' => ManageBuilder::TYPE_TEXT,
                'label' => 'services: educational institution title',
                'validation' => 'not_blank',
            ])
            ->addField('taught_subject', [
                'type' => ManageBuilder::TYPE_TEXT,
                'label' => 'services: taught subject',
                'validation' => 'not_blank',
            ])
            ->addField('class', [
                'type' => ManageBuilder::TYPE_RELATION,
                'label' => 'services: class',
                'entity' => 'Datasets.Class',
                'values' => Registry::loadRepository('Relations.Item')->getEntityRelations($service, 'Datasets.Class'),
                'validation' => 'not_blank',
                'multiple' => true,
            ])
            ->addField('phone', [
                'type' => ManageBuilder::TYPE_TEXT,
                'label' => 'contact phone',
                'value' => $entity->phone ?? $user->phone ?? null,
                'validation' => ['not_blank', new Phone()],
                'col' => 'col-sm-6',
            ])
            ->addField('email', [
                'type' => ManageBuilder::TYPE_TEXT,
                'label' => 'contact email',
                'value' => $entity->email ?? (!empty($user) ? $user->getEmail() : null),
                'validation' => ['not_blank', 'email'],
                'col' => 'col-sm-6'
            ])
            ->addField('comment', [
                'type' => ManageBuilder::TYPE_TEXTAREA,
                'label' => 'comment',
            ])

            ->addField('service_price', [
                'type' => ManageBuilder::TYPE_MONEY,
                'label' => 'services: service price',
                'col' => 'col-sm-6',
            ])

            ->addTab('invoice', ['label' => 'services: invoice', 'icon' => 'fa-file-pdf'])
            ->addField('invoice_required', [
                'type' => ManageBuilder::TYPE_CHECKBOX,
                'id' => 'invoice_required',
                'tab' => 'invoice',
            ])
            ->addField('buyer_title', [
                'type' => ManageBuilder::TYPE_TEXT,
                'validation' => 'not_blank',
                'conditionals' => $invoiceConditionals,
                'col' => 'col-sm-6',
                'tab' => 'invoice',
            ])
            ->addField('buyer_code', [
                'type' => ManageBuilder::TYPE_TEXT,
                'validation' => 'not_blank',
                'conditionals' => $invoiceConditionals,
                'col' => 'col-sm-6',
                'tab' => 'invoice',
            ])
            ->addField('buyer_vat_code', [
                'type' => ManageBuilder::TYPE_TEXT,
                'conditionals' => $invoiceConditionals,
                'col' => 'col-sm-6',
                'tab' => 'invoice',
            ])
            ->addField('buyer_address', [
                'type' => ManageBuilder::TYPE_TEXT,
                'validation' => 'not_blank',
                'conditionals' => $invoiceConditionals,
                'col' => 'col-sm-6',
                'tab' => 'invoice',
            ])

            ->addTab('participants', ['label' => 'services: pupils', 'icon' => 'fa-users'])
            ->addField('participants', [
                'type' => ManageBuilder::TYPE_BLOCK,
                'render_label' => false,
                'load' => 'services.view',
                'action' => '_manage_participants_block',
                'tab' => 'participants',
            ])
        ;
    }
}
